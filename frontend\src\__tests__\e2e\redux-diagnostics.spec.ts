import { test, expect, Page } from '@playwright/test';

/**
 * Redux Diagnostics Test Suite
 * Comprehensive end-to-end testing to identify Redux issues:
 * ❌ Missing Redux slices
 * ❌ Incorrect state structure  
 * ❌ Authentication inconsistencies
 * ❌ Middleware conflicts
 * ❌ Context API remnants
 */

interface ReduxState {
  auth: any;
  events: any;
  admin: any;
  language: any;
  incubator: any;
  forum: any;
  ai: any;
  businessPlans: any;
  aiContext: any;
  dashboard: any;
  toast: any;
  ui: any;
}

interface AuthState {
  user: any;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

test.describe('Redux Diagnostics Suite', () => {
  let page: Page;

  test.beforeEach(async ({ page: testPage }) => {
    page = testPage;
    
    // Navigate to the app and wait for Redux store to initialize
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // Wait for Redux store to be available
    await page.waitForFunction(() => window.__REDUX_STORE__ || window.store);
  });

  test('🔍 Redux Store Structure Validation', async () => {
    console.log('🧪 Testing Redux store structure...');
    
    // Get Redux state from the browser
    const reduxState = await page.evaluate(() => {
      // Try multiple ways to access the store
      const store = (window as any).__REDUX_STORE__ || 
                   (window as any).store || 
                   (window as any).__store__;
      
      if (!store) {
        throw new Error('Redux store not found on window object');
      }
      
      return store.getState();
    });

    // Validate all expected slices are present
    const expectedSlices = [
      'auth', 'events', 'admin', 'language', 'incubator', 
      'forum', 'ai', 'businessPlans', 'aiContext', 
      'dashboard', 'toast', 'ui'
    ];

    const missingSlices: string[] = [];
    const presentSlices: string[] = [];

    expectedSlices.forEach(slice => {
      if (reduxState[slice]) {
        presentSlices.push(slice);
      } else {
        missingSlices.push(slice);
      }
    });

    console.log('✅ Present slices:', presentSlices);
    if (missingSlices.length > 0) {
      console.log('❌ Missing slices:', missingSlices);
    }

    // Assert all slices are present
    expect(missingSlices).toHaveLength(0);
    expect(Object.keys(reduxState)).toEqual(expect.arrayContaining(expectedSlices));
  });

  test('🔐 Authentication State Consistency', async () => {
    console.log('🧪 Testing authentication state consistency...');
    
    const authState = await page.evaluate(() => {
      const store = (window as any).__REDUX_STORE__ || (window as any).store;
      return store.getState().auth;
    });

    // Validate auth state structure
    expect(authState).toHaveProperty('user');
    expect(authState).toHaveProperty('isAuthenticated');
    expect(authState).toHaveProperty('isLoading');
    expect(authState).toHaveProperty('error');

    // Check for authentication consistency
    const hasToken = await page.evaluate(() => {
      return localStorage.getItem('access_token') || localStorage.getItem('authToken');
    });

    const isAuthenticated = authState.isAuthenticated;
    const hasUser = authState.user !== null;

    console.log('🔍 Auth State Analysis:', {
      hasToken: !!hasToken,
      isAuthenticated,
      hasUser,
      user: authState.user
    });

    // If there's a token, user should be authenticated
    if (hasToken) {
      expect(isAuthenticated).toBe(true);
      expect(hasUser).toBe(true);
    }
  });

  test('🔄 Middleware Functionality Test', async () => {
    console.log('🧪 Testing Redux middleware...');
    
    // Test auth middleware by triggering an auth action
    await page.evaluate(() => {
      const store = (window as any).__REDUX_STORE__ || (window as any).store;
      
      // Dispatch a test action to see if middleware intercepts
      store.dispatch({
        type: 'auth/test-middleware',
        payload: { test: true }
      });
    });

    // Test AI context middleware
    await page.evaluate(() => {
      const store = (window as any).__REDUX_STORE__ || (window as any).store;
      
      // Dispatch navigation change to test aiContextMiddleware
      store.dispatch({
        type: 'navigation/locationChanged',
        payload: '/dashboard'
      });
    });

    // Verify middleware didn't cause errors
    const consoleErrors = await page.evaluate(() => {
      return (window as any).__REDUX_ERRORS__ || [];
    });

    expect(consoleErrors).toHaveLength(0);
  });

  test('🚫 Context API Remnants Detection', async () => {
    console.log('🧪 Checking for Context API remnants...');
    
    // Check for old context providers in the DOM
    const contextProviders = await page.evaluate(() => {
      const providers = [
        'AIContextProvider',
        'DashboardProvider', 
        'AuthProvider',
        'ThemeProvider'
      ];
      
      const foundProviders: string[] = [];
      
      providers.forEach(provider => {
        // Check if provider exists in DOM or as a global
        if (document.querySelector(`[data-provider="${provider}"]`) ||
            (window as any)[provider]) {
          foundProviders.push(provider);
        }
      });
      
      return foundProviders;
    });

    console.log('🔍 Context providers found:', contextProviders);
    
    // Should not find old context providers
    expect(contextProviders).toHaveLength(0);
  });

  test('📊 State Shape Validation', async () => {
    console.log('🧪 Validating Redux state shapes...');
    
    const fullState = await page.evaluate(() => {
      const store = (window as any).__REDUX_STORE__ || (window as any).store;
      return store.getState();
    });

    // Validate auth state shape
    const authState = fullState.auth;
    expect(typeof authState.isAuthenticated).toBe('boolean');
    expect(typeof authState.isLoading).toBe('boolean');
    expect(authState.error === null || typeof authState.error === 'string').toBe(true);

    // Validate UI state shape
    const uiState = fullState.ui;
    expect(uiState).toHaveProperty('sidebarOpen');
    expect(uiState).toHaveProperty('sidebarCollapsed');
    expect(uiState).toHaveProperty('theme');
    expect(typeof uiState.sidebarOpen).toBe('boolean');
    expect(typeof uiState.sidebarCollapsed).toBe('boolean');

    // Validate AI context state shape
    const aiContextState = fullState.aiContext;
    expect(aiContextState).toHaveProperty('currentPage');
    expect(aiContextState).toHaveProperty('userPreferences');
    expect(aiContextState).toHaveProperty('isAIEnabled');
    expect(typeof aiContextState.isAIEnabled).toBe('boolean');

    console.log('✅ All state shapes are valid');
  });

  test('🔄 Action Dispatch and State Updates', async () => {
    console.log('🧪 Testing action dispatch and state updates...');
    
    // Test UI state updates
    const initialSidebarState = await page.evaluate(() => {
      const store = (window as any).__REDUX_STORE__ || (window as any).store;
      return store.getState().ui.sidebarOpen;
    });

    // Dispatch sidebar toggle action
    await page.evaluate(() => {
      const store = (window as any).__REDUX_STORE__ || (window as any).store;
      store.dispatch({
        type: 'ui/toggleSidebar'
      });
    });

    const updatedSidebarState = await page.evaluate(() => {
      const store = (window as any).__REDUX_STORE__ || (window as any).store;
      return store.getState().ui.sidebarOpen;
    });

    // State should have changed
    expect(updatedSidebarState).toBe(!initialSidebarState);

    console.log('✅ Action dispatch working correctly');
  });

  test('🌐 Language State Integration', async () => {
    console.log('🧪 Testing language state integration...');
    
    const languageState = await page.evaluate(() => {
      const store = (window as any).__REDUX_STORE__ || (window as any).store;
      return store.getState().language;
    });

    // Validate language state structure
    expect(languageState).toHaveProperty('currentLanguage');
    expect(languageState).toHaveProperty('isLoading');
    expect(['en', 'ar'].includes(languageState.currentLanguage)).toBe(true);

    // Test language change
    await page.evaluate(() => {
      const store = (window as any).__REDUX_STORE__ || (window as any).store;
      store.dispatch({
        type: 'language/setLanguage',
        payload: 'ar'
      });
    });

    const updatedLanguageState = await page.evaluate(() => {
      const store = (window as any).__REDUX_STORE__ || (window as any).store;
      return store.getState().language;
    });

    expect(updatedLanguageState.currentLanguage).toBe('ar');
    console.log('✅ Language state integration working');
  });

  test('🚨 Error Handling and Recovery', async () => {
    console.log('🧪 Testing error handling and recovery...');

    // Dispatch an invalid action to test error handling
    const errorResult = await page.evaluate(() => {
      try {
        const store = (window as any).__REDUX_STORE__ || (window as any).store;

        // Dispatch invalid action
        store.dispatch({
          type: 'invalid/action',
          payload: { invalid: true }
        });

        return { success: true, error: null };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    // Redux should handle invalid actions gracefully
    expect(errorResult.success).toBe(true);

    // Check that store is still functional after error
    const stateAfterError = await page.evaluate(() => {
      const store = (window as any).__REDUX_STORE__ || (window as any).store;
      return store.getState();
    });

    expect(stateAfterError).toBeDefined();
    expect(typeof stateAfterError).toBe('object');

    console.log('✅ Error handling working correctly');
  });

  test('🔗 Token and Authentication Flow', async () => {
    console.log('🧪 Testing complete authentication flow...');

    // Clear any existing auth state
    await page.evaluate(() => {
      localStorage.clear();
      sessionStorage.clear();
    });

    // Navigate to login page
    await page.goto('/login');
    await page.waitForLoadState('networkidle');

    // Check initial auth state
    const initialAuthState = await page.evaluate(() => {
      const store = (window as any).__REDUX_STORE__ || (window as any).store;
      return store.getState().auth;
    });

    expect(initialAuthState.isAuthenticated).toBe(false);
    expect(initialAuthState.user).toBeNull();

    console.log('✅ Initial auth state correct');
  });

  test('🎯 Redux DevTools Integration', async () => {
    console.log('🧪 Testing Redux DevTools integration...');

    const hasDevTools = await page.evaluate(() => {
      return !!(window as any).__REDUX_DEVTOOLS_EXTENSION_COMPOSE__ ||
             !!(window as any).__REDUX_DEVTOOLS_EXTENSION__;
    });

    console.log('🔍 Redux DevTools available:', hasDevTools);

    // In development, DevTools should be available
    if (process.env.NODE_ENV === 'development') {
      expect(hasDevTools).toBe(true);
    }
  });

  test('📱 Responsive State Management', async () => {
    console.log('🧪 Testing responsive state management...');

    // Test mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    await page.waitForTimeout(1000);

    const mobileUIState = await page.evaluate(() => {
      const store = (window as any).__REDUX_STORE__ || (window as any).store;
      return store.getState().ui;
    });

    // Test desktop viewport
    await page.setViewportSize({ width: 1920, height: 1080 });
    await page.waitForTimeout(1000);

    const desktopUIState = await page.evaluate(() => {
      const store = (window as any).__REDUX_STORE__ || (window as any).store;
      return store.getState().ui;
    });

    // UI state should be consistent across viewports
    expect(typeof mobileUIState.sidebarOpen).toBe('boolean');
    expect(typeof desktopUIState.sidebarOpen).toBe('boolean');

    console.log('✅ Responsive state management working');
  });
});
