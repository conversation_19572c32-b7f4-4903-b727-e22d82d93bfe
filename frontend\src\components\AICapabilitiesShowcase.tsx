/**
 * AI Capabilities Showcase Component
 * Demonstrates the technical AI capabilities and features
 */

import React, { useState } from 'react';
import {
  Brain,
  MessageSquare,
  BarChart3,
  Target,
  TrendingUp,
  Users,
  Lightbulb,
  Zap,
  Eye,
  Clock,
  CheckCircle,
  ArrowRight,
  Play,
  Pause,
  RefreshCw,
} from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../hooks/useLanguage';

interface AICapabilitiesShowcaseProps {
  language?: string;
  className?: string;
}

const AICapabilitiesShowcase: React.FC<AICapabilitiesShowcaseProps> = ({ language = 'en',
  className = '',
 }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const [activeDemo, setActiveDemo] = useState<string | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);

  const aiCapabilities = [
    {
      id: 'business-analysis',
      icon: <BarChart3 className="w-6 h-6" />,
      title: language === 'ar' ? 'تحليل الأعمال الذكي' : t("ai.intelligent.business.analysis", "Intelligent Business Analysis"),
      description: language === 'ar'
        ? 'تحليل شامل للأفكار التجارية مع نقاط الجدوى والتوصيات'
        : t("ai.comprehensive.business.idea", "Comprehensive business idea analysis with viability scoring and recommendations"),
      features: [
        language === 'ar' ? 'نقاط الجدوى (0-10)' : 'Viability Scoring (0-10)',
        language === 'ar' ? 'تحليل SWOT' : t("ai.swot.analysis", "SWOT Analysis"),
        language === 'ar' ? 'بحث المنافسين' : t("ai.competitor.research", "Competitor Research"),
        language === 'ar' ? 'تقييم المخاطر' : t("ai.risk.assessment", "Risk Assessment"),
      ],
      demoData: {
        score: 8.2,
        level: language === 'ar' ? 'عالي' : 'High',
        insights: 3,
        recommendations: 5,
      },
    },
    {
      id: 'proactive-advisor',
      icon: <Brain className="w-6 h-6" />,
      title: language === 'ar' ? 'المستشار الاستباقي' : t("ai.proactive.advisor", "Proactive Advisor"),
      description: language === 'ar'
        ? 'ذكاء اصطناعي يراقب التقدم ويقدم إرشادات شخصية'
        : t("ai.ai.that.monitors", "AI that monitors progress and provides personalized guidance"),
      features: [
        language === 'ar' ? 'مراقبة التقدم' : t("ai.progress.monitoring", "Progress Monitoring"),
        language === 'ar' ? 'توصيات شخصية' : t("ai.personalized.recommendations", "Personalized Recommendations"),
        language === 'ar' ? 'تنبيهات ذكية' : t("ai.smart.alerts", "Smart Alerts"),
        language === 'ar' ? 'تتبع المعالم' : t("ai.milestone.tracking", "Milestone Tracking"),
      ],
      demoData: {
        healthScore: 73,
        nextActions: 4,
        alerts: 2,
        stage: language === 'ar' ? 'التحقق' : t("ai.validation", "Validation"),
      },
    },
    {
      id: 'market-intelligence',
      icon: <TrendingUp className="w-6 h-6" />,
      title: language === 'ar' ? 'ذكاء السوق' : t("ai.market.intelligence", "Market Intelligence"),
      description: language === 'ar'
        ? 'رؤى السوق في الوقت الفعلي واكتشاف الفرص'
        : t("ai.realtime.market.insights", "Real-time market insights and opportunity detection"),
      features: [
        language === 'ar' ? 'اتجاهات السوق' : t("ai.market.trends", "Market Trends"),
        language === 'ar' ? 'تحليل الفرص' : t("ai.opportunity.analysis", "Opportunity Analysis"),
        language === 'ar' ? 'ذكاء المنافسين' : t("ai.competitive.intelligence", "Competitive Intelligence"),
        language === 'ar' ? 'توقعات النمو' : t("ai.growth.predictions", "Growth Predictions"),
      ],
      demoData: {
        opportunities: 7,
        trends: 12,
        marketSize: language === 'ar' ? '$2.3 مليار' : '$2.3B',
        growth: '+15%',
      },
    },
    {
      id: 'smart-networking',
      icon: <Users className="w-6 h-6" />,
      title: language === 'ar' ? 'الشبكات الذكية' : t("ai.smart.networking", "Smart Networking"),
      description: language === 'ar'
        ? 'مطابقة ذكية مع الموجهين والمستثمرين والشركاء'
        : t("ai.intelligent.matching.with", "Intelligent matching with mentors, investors, and partners"),
      features: [
        language === 'ar' ? 'مطابقة الموجهين' : t("ai.mentor.matching", "Mentor Matching"),
        language === 'ar' ? 'توافق المستثمرين' : t("ai.investor.compatibility", "Investor Compatibility"),
        language === 'ar' ? 'شراكات استراتيجية' : t("ai.strategic.partnerships", "Strategic Partnerships"),
        language === 'ar' ? 'تحليل الشبكة' : t("ai.network.analysis", "Network Analysis"),
      ],
      demoData: {
        mentorMatches: 8,
        compatibility: 94,
        investors: 12,
        connections: 156,
      },
    },
  ];

  const handleDemoPlay = (capabilityId: string) => {
    setActiveDemo(capabilityId);
    setIsPlaying(true);

    // Simulate demo animation
    setTimeout(() => {
      setIsPlaying(false);
    }, 3000);
  };

  const renderDemoVisualization = (capability: any) => {
    if (activeDemo !== capability.id) return null;

    return (
      <div className="mt-4 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-200">
        <div className={`flex items-center justify-between mb-3 ${isRTL ? "flex-row-reverse" : ""}`}>
          <h4 className="font-medium text-blue-900">
            {language === 'ar' ? 'عرض توضيحي مباشر' : t("common.live.demo", "Live Demo")}
          </h4>
          {isPlaying && <RefreshCw className="w-4 h-4 animate-spin text-blue-500" />}
        </div>

        <div className="grid grid-cols-2 gap-4 text-sm">
          {Object.entries(capability.demoData).map(([key, value]) => (
            <div key={key} className={`flex justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
              <span className="text-blue-700 capitalize">{key.replace(/([A-Z])/g, ' $1')}:</span>
              <span className="font-medium text-blue-900">{value}</span>
            </div>
          ))}
        </div>
      </div>
    );
  };

  return (
    <section className={`py-16 bg-white ${className}`} dir={language === 'ar' ? 'rtl' : 'ltr'}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-16">
          <div className={`flex items-center justify-center mb-4 ${isRTL ? "flex-row-reverse" : ""}`}>
            <Zap className={`w-8 h-8 text-yellow-500 mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
            <Brain className="w-10 h-10 text-blue-500" />
            <Zap className={`w-8 h-8 text-yellow-500 ml-2 ${isRTL ? "space-x-reverse" : ""}`} />
          </div>
          <h2 className="text-4xl font-bold text-gray-900 mb-4">
            {language === 'ar' ? 'قدرات الذكاء الاصطناعي' : t("common.ai.capabilities", "AI Capabilities")}
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            {language === 'ar'
              ? 'اكتشف كيف يعمل الذكاء الاصطناعي المتقدم لدينا كشريك أعمال ذكي لتسريع نجاحك الريادي'
              : t("common.discover.how.our", "Discover how our advanced AI works as an intelligent business partner to accelerate your entrepreneurial success")
            }
          </p>
        </div>

        {/* Capabilities Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-16">
          {aiCapabilities.map((capability) => (
            <div
              key={capability.id}
              className="bg-white border-2 border-gray-200 rounded-xl p-6 hover:border-blue-300 hover:shadow-lg transition-all duration-300"
            >
              <div className={`flex items-center mb-4 ${isRTL ? "flex-row-reverse" : ""}`}>
                <div className={`p-3 bg-blue-100 rounded-lg mr-4 ${isRTL ? "space-x-reverse" : ""}`}>
                  {capability.icon}
                </div>
                <div className={`flex-1 ${isRTL ? "flex-row-reverse" : ""}`}>
                  <h3 className="text-xl font-semibold text-gray-900 mb-1">
                    {capability.title}
                  </h3>
                  <p className="text-gray-600 text-sm">
                    {capability.description}
                  </p>
                </div>
              </div>

              {/* Features List */}
              <div className="mb-4">
                <div className="grid grid-cols-2 gap-2">
                  {capability.features.map((feature, index) => (
                    <div key={index} className={`flex items-center text-sm text-gray-700 ${isRTL ? "flex-row-reverse" : ""}`}>
                      <CheckCircle className={`w-4 h-4 text-green-500 mr-2 flex-shrink-0 ${isRTL ? "flex-row-reverse" : ""}`} />
                      {feature}
                    </div>
                  ))}
                </div>
              </div>

              {/* Demo Button */}
              <button
                onClick={() => handleDemoPlay(capability.id)}
                disabled={isPlaying && activeDemo === capability.id}
                className={`w-full flex items-center justify-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 transition-colors ${isRTL ? "flex-row-reverse" : ""}`}
              >
                {isPlaying && activeDemo === capability.id ? (
                  <>
                    <RefreshCw className={`w-4 h-4 mr-2 animate-spin ${isRTL ? "space-x-reverse" : ""}`} />
                    {language === 'ar' ? 'جاري التشغيل...' : t("common.running.demo", "Running Demo...")}
                  </>
                ) : (
                  <>
                    <Play className={`w-4 h-4 mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
                    {language === 'ar' ? 'عرض توضيحي' : t("common.try.demo", "Try Demo")}
                  </>
                )}
              </button>

              {/* Demo Visualization */}
              {renderDemoVisualization(capability)}
            </div>
          ))}
        </div>

        {/* AI Workflow Visualization */}
        <div className="bg-gradient-to-br from-gray-50 to-blue-50 rounded-2xl p-8 mb-16">
          <h3 className="text-2xl font-bold text-center text-gray-900 mb-8">
            {language === 'ar' ? 'كيف يعمل الذكاء الاصطناعي' : t("common.how.ai.intelligence", "How AI Intelligence Works")}
          </h3>

          <div className={`flex flex-col lg:flex-row items-center justify-between space-y-6 lg:space-y-0 lg:space-x-8 ${isRTL ? "flex-row-reverse" : ""}`}>
            {[
              {
                step: 1,
                icon: <Eye className="w-6 h-6" />,
                title: language === 'ar' ? 'يراقب' : "Monitors",
                description: language === 'ar' ? 'يتتبع تقدمك ونشاطك' : t("ai.tracks.your.progress", "Tracks your progress and activity"),
              },
              {
                step: 2,
                icon: <Brain className="w-6 h-6" />,
                title: language === 'ar' ? 'يحلل' : t("ai.analyzes", "Analyzes"),
                description: language === 'ar' ? 'يفهم السياق والتحديات' : t("ai.understands.context.and", "Understands context and challenges"),
              },
              {
                step: 3,
                icon: <Lightbulb className="w-6 h-6" />,
                title: language === 'ar' ? 'يوصي' : t("ai.recommends", "Recommends"),
                description: language === 'ar' ? 'يقترح إجراءات محددة' : t("ai.suggests.specific.actions", "Suggests specific actions"),
              },
              {
                step: 4,
                icon: <Target className="w-6 h-6" />,
                title: language === 'ar' ? 'يوجه' : t("ai.guides", "Guides"),
                description: language === 'ar' ? 'يساعدك على التنفيذ' : t("ai.helps.you.execute", "Helps you execute"),
              },
            ].map((step, index) => (
              <div key={index} className={`flex flex-col items-center text-center max-w-xs ${isRTL ? "flex-row-reverse" : ""}`}>
                <div className={`w-16 h-16 bg-blue-500 text-white rounded-full flex items-center justify-center mb-4 ${isRTL ? "flex-row-reverse" : ""}`}>
                  {step.icon}
                </div>
                <div className={`w-8 h-8 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-bold mb-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                  {step.step}
                </div>
                <h4 className="font-semibold text-gray-900 mb-2">{step.title}</h4>
                <p className="text-sm text-gray-600">{step.description}</p>

                {index < 3 && (
                  <ArrowRight className="w-6 h-6 text-blue-400 mt-4 lg:hidden" />
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Technical Specifications */}
        <div className="bg-white border-2 border-gray-200 rounded-xl p-8">
          <h3 className="text-2xl font-bold text-center text-gray-900 mb-8">
            {language === 'ar' ? 'المواصفات التقنية' : t("common.technical.specifications", "Technical Specifications")}
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[
              {
                label: language === 'ar' ? 'نماذج الذكاء الاصطناعي' : t("ai.ai.models", "AI Models"),
                value: language === 'ar' ? 'متعددة' : t("ai.multiple", "Multiple"),
                description: language === 'ar' ? 'GPT-4، Claude، Gemini' : 'GPT-4, Claude, Gemini',
              },
              {
                label: language === 'ar' ? 'التحديثات' : t("ai.updates", "Updates"),
                value: language === 'ar' ? 'فوري' : t("ai.realtime", "Real-time"),
                description: language === 'ar' ? 'تحليل مستمر' : t("ai.continuous.analysis", "Continuous analysis"),
              },
              {
                label: language === 'ar' ? 'الدقة' : t("ai.accuracy", "Accuracy"),
                value: '94%',
                description: language === 'ar' ? 'توصيات دقيقة' : t("ai.accurate.recommendations", "Accurate recommendations"),
              },
              {
                label: language === 'ar' ? 'اللغات' : t("ai.languages", "Languages"),
                value: language === 'ar' ? 'متعددة' : t("ai.multi", "Multi"),
                description: language === 'ar' ? 'عربي وإنجليزي' : 'Arabic & English',
              },
            ].map((spec, index) => (
              <div key={index} className="text-center">
                <div className="text-2xl font-bold text-blue-600 mb-1">{spec.value}</div>
                <div className="font-medium text-gray-900 mb-1">{spec.label}</div>
                <div className="text-sm text-gray-600">{spec.description}</div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default AICapabilitiesShowcase;
