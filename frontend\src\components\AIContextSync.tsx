import React, { useEffect } from 'react';
import { useAppSelector, useAppDispatch } from '../store/hooks';
import { loadUserPreferences, setUserId } from '../store/aiContextSlice';
import { useAILocationSync } from '../hooks/useAILocationSync';

/**
 * Component to sync AI context with app state
 * This replaces the AIContextProvider functionality
 */
const AIContextSync: React.FC = () => {
  const dispatch = useAppDispatch();
  const { user } = useAppSelector(state => state.auth);

  // Sync location changes with AI context
  useAILocationSync();

  // Load user preferences and set user ID when user changes
  useEffect(() => {
    if (user) {
      dispatch(setUserId(user.id));
      dispatch(loadUserPreferences());
    }
  }, [dispatch, user]);

  // This component doesn't render anything, it just handles side effects
  return null;
};

export default AIContextSync;
