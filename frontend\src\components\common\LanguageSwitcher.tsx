import React, { useState } from 'react';
import { Globe, ChevronDown } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';

interface LanguageSwitcherProps {
  className?: string;
  variant?: 'sidebar' | 'dropdown' | 'compact';
}

const LanguageSwitcher: React.FC<LanguageSwitcherProps> = ({ 
  className = '', 
  variant = 'sidebar' 
}) => {
  const { t, i18n } = useTranslation();
  const { isRTL, language } = useLanguage();
  const [isOpen, setIsOpen] = useState(false);

  const languages = [
    { code: 'en', name: 'English', flag: '🇺🇸' },
    { code: 'ar', name: 'العربية', flag: '🇸🇦' }
  ];

  const currentLanguage = languages.find(lang => lang.code === language) || languages[0];

  const handleLanguageChange = (langCode: string) => {
    i18n.changeLanguage(langCode);
    setIsOpen(false);
  };

  if (variant === 'compact') {
    return (
      <div className={`relative ${className}`}>
        <button
          onClick={() => setIsOpen(!isOpen)}
          className={`flex items-center px-3 py-2 text-gray-300 hover:bg-white/20 hover:text-white rounded-lg transition-colors ${isRTL ? 'flex-row-reverse' : ''}`}
          aria-label={t('changeLanguage', 'تغيير اللغة')}
        >
          <Globe size={16} className={isRTL ? 'ml-2' : 'mr-2'} />
          <span className="text-sm">{currentLanguage.flag}</span>
        </button>

        {isOpen && (
          <>
            {/* Backdrop */}
            <div 
              className="fixed inset-0 z-10" 
              onClick={() => setIsOpen(false)}
            />
            
            {/* Dropdown */}
            <div className={`absolute top-full mt-1 ${isRTL ? 'right-0' : 'left-0'} z-20 bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg shadow-lg min-w-[120px]`}>
              {languages.map((lang) => (
                <button
                  key={lang.code}
                  onClick={() => handleLanguageChange(lang.code)}
                  className={`w-full flex items-center px-3 py-2 text-sm hover:bg-white/20 transition-colors ${
                    language === lang.code ? 'bg-purple-600/30 text-white' : 'text-gray-300'
                  } ${isRTL ? 'flex-row-reverse text-right' : 'text-left'} first:rounded-t-lg last:rounded-b-lg`}
                >
                  <span className={isRTL ? 'ml-2' : 'mr-2'}>{lang.flag}</span>
                  <span>{lang.name}</span>
                </button>
              ))}
            </div>
          </>
        )}
      </div>
    );
  }

  if (variant === 'dropdown') {
    return (
      <div className={`relative ${className}`}>
        <button
          onClick={() => setIsOpen(!isOpen)}
          className={`flex items-center justify-between w-full px-3 py-2 text-gray-300 hover:bg-white/20 hover:text-white rounded-lg transition-colors ${isRTL ? 'flex-row-reverse' : ''}`}
        >
          <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
            <Globe size={16} className={isRTL ? 'ml-2' : 'mr-2'} />
            <span className="text-sm">{currentLanguage.name}</span>
          </div>
          <ChevronDown size={14} className={`transition-transform ${isOpen ? 'rotate-180' : ''}`} />
        </button>

        {isOpen && (
          <>
            {/* Backdrop */}
            <div 
              className="fixed inset-0 z-10" 
              onClick={() => setIsOpen(false)}
            />
            
            {/* Dropdown */}
            <div className={`absolute top-full mt-1 ${isRTL ? 'right-0' : 'left-0'} z-20 bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg shadow-lg w-full min-w-[140px]`}>
              {languages.map((lang) => (
                <button
                  key={lang.code}
                  onClick={() => handleLanguageChange(lang.code)}
                  className={`w-full flex items-center px-3 py-2 text-sm hover:bg-white/20 transition-colors ${
                    language === lang.code ? 'bg-purple-600/30 text-white' : 'text-gray-300'
                  } ${isRTL ? 'flex-row-reverse text-right' : 'text-left'} first:rounded-t-lg last:rounded-b-lg`}
                >
                  <span className={isRTL ? 'ml-2' : 'mr-2'}>{lang.flag}</span>
                  <span>{lang.name}</span>
                </button>
              ))}
            </div>
          </>
        )}
      </div>
    );
  }

  // Default sidebar variant
  return (
    <div className={`${className}`}>
      <div className={`flex items-center justify-between mb-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
        <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
          <Globe size={16} className={`text-purple-400 ${isRTL ? 'ml-2' : 'mr-2'}`} />
          <span className="text-sm font-medium text-gray-300">{t('language', 'اللغة')}</span>
        </div>
      </div>
      
      <div className="space-y-1">
        {languages.map((lang) => (
          <button
            key={lang.code}
            onClick={() => handleLanguageChange(lang.code)}
            className={`w-full flex items-center px-3 py-2 text-sm rounded-lg transition-colors ${
              language === lang.code 
                ? 'bg-purple-600/30 text-white' 
                : 'text-gray-300 hover:bg-white/20 hover:text-white'
            } ${isRTL ? 'flex-row-reverse text-right' : 'text-left'}`}
          >
            <span className={isRTL ? 'ml-2' : 'mr-2'}>{lang.flag}</span>
            <span>{lang.name}</span>
          </button>
        ))}
      </div>
    </div>
  );
};

export default LanguageSwitcher;
