import React, { useState } from 'react';
import { useLocation } from 'react-router-dom';
import { useAppSelector } from '../../store/hooks';
import { useLanguage } from '../../hooks/useLanguage';
import { isSuperAdmin } from '../../utils/roleBasedRouting';
import UniversalSidebar from './UniversalSidebar';
// Simplified layout - no debug components
import { Menu } from 'lucide-react';

interface AIFocusedLayoutProps {
  children: React.ReactNode;
}

/**
 * AIFocusedLayout - For AI chat and analysis pages
 * Shows: Sidebar + Content (no footer)
 * Provides: Navigation access with clean chat interface
 * Matches AuthenticatedLayout structure exactly
 */
const AIFocusedLayout: React.FC<AIFocusedLayoutProps> = ({ children }) => {
  const { isRTL } = useLanguage();
  const { user, isAuthenticated } = useAppSelector(state => state.auth);
  const location = useLocation();
  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false);
  const [isDesktopSidebarCollapsed, setIsDesktopSidebarCollapsed] = useState(false);

  return (
    <div className={`min-h-screen flex ${isRTL ? "flex-row-reverse" : ""}`}>
      {/* Universal Sidebar for authenticated users - EXACT SAME AS AuthenticatedLayout */}
      <>
        {/* Desktop Sidebar - Only visible on large screens */}
        <UniversalSidebar
          variant="desktop"
          isCollapsed={isDesktopSidebarCollapsed}
          onToggle={() => setIsDesktopSidebarCollapsed(!isDesktopSidebarCollapsed)}
        />

        {/* Mobile Sidebar - Re-enabled with updated styling */}
        <UniversalSidebar
          variant="mobile"
          isOpen={isMobileSidebarOpen}
          onClose={() => setIsMobileSidebarOpen(false)}
        />
      </>

      {/* Main Content Area */}
      <div className="flex-1 flex flex-col min-h-screen">
        {/* Mobile Menu Button - EXACT SAME AS AuthenticatedLayout */}
        <button
          onClick={() => setIsMobileSidebarOpen(true)}
          className={`lg:hidden fixed top-4 ${isRTL ? 'right-4' : 'left-4'} z-50 bg-gradient-to-r from-purple-600 to-blue-600 p-4 rounded-xl shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-110 border-2 border-white/20`}
          aria-label="Open Menu"
          title="Open Menu"
        >
          <Menu className="w-7 h-7 text-white" />
        </button>

        {/* Main Content - SAME SPACING AS AuthenticatedLayout */}
        <main className={`flex-grow ${isRTL ? "flex-row-reverse" : ""} ${
          isRTL
            ? (isDesktopSidebarCollapsed ? 'lg:mr-16' : 'lg:mr-72')
            : (isDesktopSidebarCollapsed ? 'lg:ml-16' : 'lg:ml-72')
        }`}>
          {children}
        </main>

        {/* Simplified layout - no debug components */}
      </div>
    </div>
  );
};

export default AIFocusedLayout;
