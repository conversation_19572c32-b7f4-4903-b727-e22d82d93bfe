import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAppSelector } from '../../store/hooks';
import { RouteConfig, validateRouteAccess } from '../../routes/routeConfig';

import { UserRole, PermissionLevel } from '../../routes/routeConfig';
// Simple role routing - no complex role manager

interface RoleRouteProps {
  config: RouteConfig;
  children: React.ReactNode;
}

/**
 * RoleRoute component that handles role-based access control for routes
 */
const RoleRoute: React.FC<RoleRouteProps> = ({ config, children }) => {
  const location = useLocation();
  const { user, isAuthenticated, isLoading } = useAppSelector((state) => state.auth);

  // Show loading state while authentication is being checked
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  // Extract user roles using unified role manager
  const extractUserRoles = (user: any): UserRole[] => {
    if (!user) return ['user'];
    if (user.is_admin) return ['admin'];
    if (user.is_staff) return ['moderator'];
    return ['user'];
  };

  const extractUserPermissions = (user: any): PermissionLevel[] => {
    if (!user) return ['read'];

    const permissions: PermissionLevel[] = ['read'];

    if (user.is_admin) {
      permissions.push('write', 'moderate', 'admin', 'super_admin');
    } else if (user.is_staff) {
      permissions.push('write', 'moderate');
    } else if (user.is_mentor || user.is_investor) {
      permissions.push('write');
    }

    return permissions;
  };

  // Check if user has access to this route
  const userRoles = extractUserRoles(user);
  const userPermissions = extractUserPermissions(user);
  const isAdmin = user?.is_admin === true;
  const isSuperAdminUser = user?.is_admin === true;

  const hasAccess = validateRouteAccess(
    config,
    userRoles,
    userPermissions,
    isAuthenticated,
    isAdmin,
    isSuperAdminUser
  );

  // If user doesn't have access, redirect to appropriate page
  if (!hasAccess) {
    const redirectTo = config.redirectTo || '/login';
    
    // If not authenticated, redirect to login with return URL
    if (!isAuthenticated) {
      return <Navigate to={`/login?returnUrl=${encodeURIComponent(location.pathname)}`} replace />;
    }
    
    // If authenticated but insufficient permissions, redirect to configured path
    return <Navigate to={redirectTo} replace />;
  }

  // User has access, render the children
  return <>{children}</>;
};

export default RoleRoute;
