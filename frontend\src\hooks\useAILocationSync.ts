import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { useAppDispatch } from '../store/hooks';
import { updateCurrentPage } from '../store/aiContextSlice';

/**
 * Hook to sync location changes with AI context Redux state
 * This replaces the useLocation logic that was in AIContextProvider
 */
export const useAILocationSync = () => {
  const location = useLocation();
  const dispatch = useAppDispatch();

  useEffect(() => {
    dispatch(updateCurrentPage(location.pathname));
  }, [dispatch, location.pathname]);
};

export default useAILocationSync;
