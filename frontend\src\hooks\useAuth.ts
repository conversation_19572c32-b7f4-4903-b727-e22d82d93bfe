import { useAppSelector, useAppDispatch } from '../store/hooks';
import { login, logout, register, getCurrentUser, clearError } from '../store/authSlice';

/**
 * Simple authentication hook using Redux
 */
export const useAuth = () => {
  const dispatch = useAppDispatch();
  const { user, isAuthenticated, isLoading, error } = useAppSelector(state => state.auth);

  return {
    // State
    user,
    isAuthenticated,
    isLoading,
    error,
    
    // Actions
    login: (credentials: { username: string; password: string }) => dispatch(login(credentials)),
    logout: () => dispatch(logout()),
    register: (userData: any) => dispatch(register(userData)),
    getCurrentUser: () => dispatch(getCurrentUser()),
    clearError: () => dispatch(clearError()),
  };
};

export default useAuth;