import { Middleware } from '@reduxjs/toolkit';
import { updateCurrentPage, loadUserPreferences, setUserId } from '../store/aiContextSlice';

/**
 * Middleware to handle AI context updates based on navigation and auth changes
 */
export const aiContextMiddleware: Middleware = (store) => (next) => (action) => {
  const result = next(action);

  // Handle auth state changes
  if (action.type === 'auth/getCurrentUser/fulfilled' || action.type === 'auth/login/fulfilled') {
    const state = store.getState() as any;
    const user = state.auth.user;
    
    if (user) {
      store.dispatch(setUserId(user.id));
      store.dispatch(loadUserPreferences());
    }
  }

  // Handle location changes (this would be triggered by a custom action)
  if (action.type === 'navigation/locationChanged') {
    const pathname = action.payload;
    store.dispatch(updateCurrentPage(pathname));
  }

  return result;
};

// Helper function to dispatch location changes from components
export const dispatchLocationChange = (dispatch: any, pathname: string) => {
  dispatch({ type: 'navigation/locationChanged', payload: pathname });
};
