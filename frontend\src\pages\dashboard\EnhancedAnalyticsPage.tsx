import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { BarChart2, AlertCircle, ArrowLeft, TrendingUp, Users, Target } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import { useAppSelector } from '../../store/hooks';
// Removed complex role manager - using simple user role checks
// AuthenticatedLayout removed - page is already wrapped by route layout
import { AnalyticsDashboard, CompetitiveAnalysisPanel } from '../../components/analytics';
import { BusinessIdea, businessIdeasAPI } from '../../services/incubatorApi';

const EnhancedAnalyticsPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { user } = useAppSelector(state => state.auth);
  const { t } = useTranslation();

  const [businessIdea, setBusinessIdea] = useState<BusinessIdea | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'general' | 'competitive'>('general');

  // Fetch business idea
  useEffect(() => {
    const fetchBusinessIdea = async () => {
      if (!id) {
        setError(t('incubator.analytics.businessIdeaNotFound'));
        setLoading(false);
        return;
      }

      try {
        const idea = await businessIdeasAPI.getBusinessIdea(parseInt(id));
        setBusinessIdea(idea);
        setError(null);
      } catch (err) {
        console.error('Error fetching business idea:', err);
        setError(t('incubator.analytics.failedToLoadAnalytics'));
      } finally {
        setLoading(false);
      }
    };

    fetchBusinessIdea();
  }, [id]);

  if (loading) {
    return (
      <div className="p-6 flex justify-center py-12">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full animate-spin mx-auto"></div>
          <p className="mt-4 text-gray-600">{t('Loading analytics...')}</p>
        </div>
      </div>
    );
  }

  if (error || !businessIdea) {
    return (
      <div className="p-6">
        <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-red-500/30 text-center">
          <AlertCircle size={40} className="mx-auto mb-3 text-red-400" />
          <h3 className="text-xl font-bold mb-2 text-white">{t('incubator.analytics.errorLoadingBusinessIdea')}</h3>
          <div className="text-gray-300 mb-4">
            {error || t('incubator.analytics.businessIdeaNotFound')}
          </div>
          <button
            onClick={() => navigate('/dashboard/business-ideas')}
            className="px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded-md text-white inline-flex items-center"
          >
            <ArrowLeft size={16} className="mr-1" />
            {t('incubator.analytics.backToBusinessIdeas')}
          </button>
        </div>
      </div>
    );
  }

  // Check if user has permission to view this business idea
  const hasPermission =
    user?.is_admin ||
    user?.is_staff ||
    businessIdea.owner.id === user?.id ||
    businessIdea.collaborators.some(collaborator => collaborator.id === user?.id);

  if (!hasPermission) {
    return (
      <div className="p-6">
        <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-red-500/30 text-center">
          <AlertCircle size={40} className="mx-auto mb-3 text-red-400" />
          <h3 className="text-xl font-bold mb-2 text-white">{t('incubator.analytics.accessDenied')}</h3>
          <div className="text-gray-300 mb-4">
            {t('incubator.analytics.noPermissionToView')}
          </div>
          <button
            onClick={() => navigate('/dashboard/business-ideas')}
            className="px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded-md text-white inline-flex items-center"
          >
            <ArrowLeft size={16} className="mr-1" />
            {t('incubator.analytics.backToBusinessIdeas')}
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="mb-6">
        <div className="flex items-center">
          <button
            onClick={() => navigate(`/dashboard/business-ideas/${businessIdea.id}`)}
            className="mr-4 p-2 bg-white/20 hover:bg-white/30 rounded-full"
          >
            <ArrowLeft size={20} className="text-white" />
          </button>
          <div>
            <h1 className="text-2xl font-bold text-white">{t('incubator.analytics.dashboard')}</h1>
            <div className="text-gray-300 mt-1">
              {t('incubator.analytics.advancedAnalytics')} {businessIdea.title}
            </div>
          </div>
        </div>
      </div>

      {/* Analytics Tabs */}
      <div className="mb-6 bg-white/10 backdrop-blur-sm rounded-lg p-4 border border-white/20">
        <div className="flex space-x-2">
          <button
            onClick={() => setActiveTab('general')}
            className={`px-4 py-2 flex items-center rounded-lg transition-colors ${
              activeTab === 'general'
                ? 'bg-purple-600 text-white font-medium'
                : 'text-gray-300 hover:text-white hover:bg-white/10'
            }`}
          >
            <BarChart2 size={18} className="mr-2" /> {t('incubator.analytics.generalAnalytics')}
          </button>
          <button
            onClick={() => setActiveTab('competitive')}
            className={`px-4 py-2 flex items-center rounded-lg transition-colors ${
              activeTab === 'competitive'
                ? 'bg-purple-600 text-white font-medium'
                : 'text-gray-300 hover:text-white hover:bg-white/10'
            }`}
          >
            <Target size={18} className="mr-2" /> {t('incubator.analytics.competitiveAnalysis')}
          </button>
        </div>
      </div>

      {/* Tab Content */}
      {activeTab === 'general' ? (
        <AnalyticsDashboard businessIdeaId={businessIdea.id} />
      ) : (
        <CompetitiveAnalysisPanel
          businessIdeaId={businessIdea.id}
          businessIdeaTitle={businessIdea.title}
        />
      )}
    </div>
  );
};

export default EnhancedAnalyticsPage;
