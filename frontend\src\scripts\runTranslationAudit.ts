/**
 * Run Translation Audit Script
 * Identifies and reports missing translation keys
 */

import { auditTranslations, printAuditReport, generateMissingKeysTemplate } from '../utils/translationAuditor';

async function main() {
  console.log('🚀 Starting comprehensive translation audit...\n');
  
  try {
    const result = await auditTranslations();
    
    // Print the audit report
    printAuditReport(result);
    
    // Generate templates for missing keys
    if (result.missingKeys.en.length > 0) {
      console.log('\n📝 English missing keys template:');
      console.log(generateMissingKeysTemplate(result, 'en'));
    }
    
    if (result.missingKeys.ar.length > 0) {
      console.log('\n📝 Arabic missing keys template:');
      console.log(generateMissingKeysTemplate(result, 'ar'));
    }
    
    // Summary
    const totalMissing = result.missingKeys.en.length + result.missingKeys.ar.length;
    console.log(`\n🎯 Total missing keys: ${totalMissing}`);
    
    if (totalMissing === 0) {
      console.log('✅ All translations are complete!');
    } else {
      console.log('❌ Translation system needs attention');
    }
    
  } catch (error) {
    console.error('❌ Audit failed:', error);
  }
}

// Run the audit
main().catch(console.error);
