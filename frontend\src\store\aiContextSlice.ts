import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';

// Types from the original AIContextProvider
interface AIPreferences {
  enableFloatingAssistant: boolean;
  enableRealTimeEnhancements: boolean;
  enableProactiveNotifications: boolean;
  enableFormAssistance: boolean;
  assistantPosition: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';
  notificationFrequency: 'high' | 'medium' | 'low';
  autoApplySuggestions: boolean;
  preferredLanguage: string;
}

interface ContextualHelp {
  id: string;
  page: string;
  element?: string;
  title: string;
  description: string;
  type: 'tip' | 'warning' | 'info' | 'enhancement';
  priority: number;
  conditions?: Record<string, any>;
}

interface SmartSuggestion {
  id: string;
  type: 'navigation' | 'action' | 'content' | 'optimization';
  title: string;
  description: string;
  action: () => void;
  confidence: number;
  context: string[];
}

interface PageContext {
  pageType: string;
  features: string[];
  aiRelevance: 'low' | 'medium' | 'high' | 'very-high';
}

interface AIContextState {
  currentPage: string;
  currentContext: PageContext;
  businessIdeaId?: number;
  userId?: number;
  userPreferences: AIPreferences;
  isAIEnabled: boolean;
  contextualHelp: ContextualHelp[];
  smartSuggestions: SmartSuggestion[];
  isLoading: boolean;
  error: string | null;
}

// Initial state
const initialState: AIContextState = {
  currentPage: '',
  currentContext: {
    pageType: 'unknown',
    features: [],
    aiRelevance: 'low'
  },
  userId: undefined,
  userPreferences: {
    enableFloatingAssistant: true,
    enableRealTimeEnhancements: true,
    enableProactiveNotifications: true,
    enableFormAssistance: true,
    assistantPosition: 'bottom-right',
    notificationFrequency: 'medium',
    autoApplySuggestions: false,
    preferredLanguage: 'en'
  },
  isAIEnabled: true,
  contextualHelp: [],
  smartSuggestions: [],
  isLoading: false,
  error: null
};

// Async thunk for loading user preferences
export const loadUserPreferences = createAsyncThunk(
  'aiContext/loadUserPreferences',
  async (_, { rejectWithValue }) => {
    try {
      const savedPreferences = localStorage.getItem('aiPreferences');
      if (savedPreferences) {
        return JSON.parse(savedPreferences);
      }
      return null;
    } catch (error) {
      return rejectWithValue('Failed to load AI preferences');
    }
  }
);

// Async thunk for saving user preferences
export const saveUserPreferences = createAsyncThunk(
  'aiContext/saveUserPreferences',
  async (preferences: Partial<AIPreferences>, { rejectWithValue }) => {
    try {
      localStorage.setItem('aiPreferences', JSON.stringify(preferences));
      return preferences;
    } catch (error) {
      return rejectWithValue('Failed to save AI preferences');
    }
  }
);

// Helper function to analyze page context
const analyzePageContext = (pathname: string): PageContext => {
  const context: PageContext = {
    pageType: 'unknown',
    features: [],
    aiRelevance: 'low'
  };

  if (pathname.includes('/dashboard')) {
    context.pageType = 'dashboard';
    context.features = ['overview', 'navigation', 'quick-actions'];
    context.aiRelevance = 'high';
  } else if (pathname.includes('/business-ideas')) {
    context.pageType = 'business-ideas';
    context.features = ['creation', 'editing', 'analysis'];
    context.aiRelevance = 'very-high';
  } else if (pathname.includes('/mentorship')) {
    context.pageType = 'mentorship';
    context.features = ['matching', 'communication', 'scheduling'];
    context.aiRelevance = 'high';
  } else if (pathname.includes('/funding')) {
    context.pageType = 'funding';
    context.features = ['applications', 'tracking', 'preparation'];
    context.aiRelevance = 'high';
  } else if (pathname.includes('/profile')) {
    context.pageType = 'profile';
    context.features = ['editing', 'completion', 'optimization'];
    context.aiRelevance = 'medium';
  }

  return context;
};

// Helper function to extract business idea ID
const extractBusinessIdeaId = (pathname: string): number | undefined => {
  const match = pathname.match(/\/business-ideas\/(\d+)/);
  return match ? parseInt(match[1], 10) : undefined;
};

// Helper function to generate contextual help
const generateContextualHelp = (page: string, context: PageContext): ContextualHelp[] => {
  const helpItems: ContextualHelp[] = [];

  if (page.includes('/business-ideas/create')) {
    helpItems.push({
      id: 'create-idea-help',
      page,
      title: 'AI-Powered Idea Creation',
      description: 'Use our AI assistant to enhance your business idea as you type. Get real-time suggestions for market fit and improvements.',
      type: 'tip',
      priority: 1
    });
  }

  if (page.includes('/dashboard') && context.pageType === 'dashboard') {
    helpItems.push({
      id: 'dashboard-ai-help',
      page,
      title: 'Your AI Assistant is Active',
      description: 'Your AI is continuously working to enhance your business ideas. Check the AI dashboard for recent improvements.',
      type: 'info',
      priority: 2
    });
  }

  if (page.includes('/mentorship')) {
    helpItems.push({
      id: 'mentorship-ai-help',
      page,
      title: 'Smart Mentor Matching',
      description: 'AI can analyze your business needs and match you with the most suitable mentors based on expertise and experience.',
      type: 'enhancement',
      priority: 1
    });
  }

  return helpItems;
};

const aiContextSlice = createSlice({
  name: 'aiContext',
  initialState,
  reducers: {
    // Update current page and context
    updateCurrentPage: (state, action: PayloadAction<string>) => {
      state.currentPage = action.payload;
      state.currentContext = analyzePageContext(action.payload);
      state.businessIdeaId = extractBusinessIdeaId(action.payload);
      state.contextualHelp = generateContextualHelp(action.payload, state.currentContext);
    },

    // Update context data
    updateContext: (state, action: PayloadAction<Partial<AIContextState>>) => {
      Object.assign(state, action.payload);
    },

    // Update user preferences
    updatePreferences: (state, action: PayloadAction<Partial<AIPreferences>>) => {
      state.userPreferences = { ...state.userPreferences, ...action.payload };
    },

    // Add contextual help
    addContextualHelp: (state, action: PayloadAction<ContextualHelp>) => {
      const existingIndex = state.contextualHelp.findIndex(help => help.id === action.payload.id);
      if (existingIndex >= 0) {
        state.contextualHelp[existingIndex] = action.payload;
      } else {
        state.contextualHelp.push(action.payload);
      }
    },

    // Add smart suggestion
    addSmartSuggestion: (state, action: PayloadAction<SmartSuggestion>) => {
      const existingIndex = state.smartSuggestions.findIndex(suggestion => suggestion.id === action.payload.id);
      if (existingIndex >= 0) {
        state.smartSuggestions[existingIndex] = action.payload;
      } else {
        state.smartSuggestions.push(action.payload);
      }
    },

    // Clear contextual help
    clearContextualHelp: (state) => {
      state.contextualHelp = [];
    },

    // Clear smart suggestions
    clearSmartSuggestions: (state) => {
      state.smartSuggestions = [];
    },

    // Set user ID
    setUserId: (state, action: PayloadAction<number | undefined>) => {
      state.userId = action.payload;
    },

    // Set AI enabled state
    setAIEnabled: (state, action: PayloadAction<boolean>) => {
      state.isAIEnabled = action.payload;
    },

    // Clear error
    clearError: (state) => {
      state.error = null;
    }
  },
  extraReducers: (builder) => {
    builder
      // Load user preferences
      .addCase(loadUserPreferences.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(loadUserPreferences.fulfilled, (state, action) => {
        state.isLoading = false;
        if (action.payload) {
          state.userPreferences = { ...state.userPreferences, ...action.payload };
        }
      })
      .addCase(loadUserPreferences.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      // Save user preferences
      .addCase(saveUserPreferences.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(saveUserPreferences.fulfilled, (state, action) => {
        state.isLoading = false;
        state.userPreferences = { ...state.userPreferences, ...action.payload };
      })
      .addCase(saveUserPreferences.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
  }
});

// Export actions
export const {
  updateCurrentPage,
  updateContext,
  updatePreferences,
  addContextualHelp,
  addSmartSuggestion,
  clearContextualHelp,
  clearSmartSuggestions,
  setUserId,
  setAIEnabled,
  clearError
} = aiContextSlice.actions;

// Selectors
export const selectAIContext = (state: { aiContext: AIContextState }) => state.aiContext;
export const selectCurrentPage = (state: { aiContext: AIContextState }) => state.aiContext.currentPage;
export const selectCurrentContext = (state: { aiContext: AIContextState }) => state.aiContext.currentContext;
export const selectUserPreferences = (state: { aiContext: AIContextState }) => state.aiContext.userPreferences;
export const selectContextualHelp = (state: { aiContext: AIContextState }) => state.aiContext.contextualHelp;
export const selectSmartSuggestions = (state: { aiContext: AIContextState }) => state.aiContext.smartSuggestions;
export const selectBusinessIdeaId = (state: { aiContext: AIContextState }) => state.aiContext.businessIdeaId;
export const selectIsAIEnabled = (state: { aiContext: AIContextState }) => state.aiContext.isAIEnabled;

// Helper selectors
export const selectPageContext = (state: { aiContext: AIContextState }) => ({
  page: state.aiContext.currentPage,
  context: state.aiContext.currentContext,
  businessIdeaId: state.aiContext.businessIdeaId,
  userId: state.aiContext.userId
});

export const selectIsAIRelevantPage = (state: { aiContext: AIContextState }) => {
  const relevantPages = [
    '/dashboard',
    '/business-ideas',
    '/mentorship',
    '/funding',
    '/profile',
    '/ai'
  ];

  return relevantPages.some(page => state.aiContext.currentPage.includes(page)) ||
         state.aiContext.currentContext.aiRelevance === 'high' ||
         state.aiContext.currentContext.aiRelevance === 'very-high';
};

export default aiContextSlice.reducer;
