import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { adminAPI } from '../services/api';

// Types for dashboard functionality
export type DashboardRole = 'user' | 'admin' | 'super_admin' | 'moderator' | 'mentor' | 'investor';

interface DashboardStat {
  id: string;
  title: string;
  value: string | number;
  change?: string;
  trend?: 'up' | 'down' | 'stable';
  icon?: string;
}

interface DashboardQuickAction {
  id: string;
  title: string;
  description: string;
  icon: string;
  action: string;
  href?: string;
  onClick?: () => void;
}

interface DashboardConfig {
  refreshInterval: number;
  enableRealTime: boolean;
  theme: 'light' | 'dark' | 'auto';
  layout: 'grid' | 'list';
  sections: string[];
}

interface DashboardLoadingState {
  global: boolean;
  stats: boolean;
  quickActions: boolean;
  sectionData: Record<string, boolean>;
}

interface DashboardError {
  section: string;
  message: string;
  timestamp: number;
}

interface SystemMetrics {
  cpu_usage: number;
  memory_usage: number;
  disk_usage: number;
  active_users: number;
  response_time: number;
}

interface SecurityAlert {
  id: string;
  type: 'warning' | 'error' | 'info';
  message: string;
  timestamp: string;
  resolved: boolean;
}

interface RecentActivity {
  id: string;
  type: string;
  description: string;
  timestamp: string;
  user?: string;
}

interface PendingReport {
  id: string;
  type: string;
  content: string;
  reporter: string;
  timestamp: string;
  status: 'pending' | 'reviewing' | 'resolved';
}

interface DashboardState {
  role: DashboardRole;
  config: DashboardConfig;
  
  // Common dashboard data
  stats: DashboardStat[];
  quickActions: DashboardQuickAction[];
  
  // Role-specific data
  systemMetrics: SystemMetrics | null;
  securityAlerts: SecurityAlert[];
  recentActivity: RecentActivity[];
  pendingReports: PendingReport[];
  
  // UI state
  loading: DashboardLoadingState;
  errors: DashboardError[];
  lastRefresh: number;
  
  // General state
  isLoading: boolean;
  error: string | null;
}

// Initial state
const initialState: DashboardState = {
  role: 'user',
  config: {
    refreshInterval: 30000,
    enableRealTime: true,
    theme: 'auto',
    layout: 'grid',
    sections: []
  },
  stats: [],
  quickActions: [],
  systemMetrics: null,
  securityAlerts: [],
  recentActivity: [],
  pendingReports: [],
  loading: {
    global: false,
    stats: false,
    quickActions: false,
    sectionData: {}
  },
  errors: [],
  lastRefresh: 0,
  isLoading: false,
  error: null
};

// Async thunks for different dashboard data
export const fetchDashboardStats = createAsyncThunk(
  'dashboard/fetchStats',
  async (role: DashboardRole, { rejectWithValue }) => {
    try {
      switch (role) {
        case 'admin':
        case 'super_admin':
          return await adminAPI.getAllStats();
        default:
          // For regular users, return basic stats
          return {
            users: { total_users: 0, active_users: 0, new_users: 0 },
            events: { total_events: 0, upcoming_events: 0, new_events: 0 },
            resources: { total_resources: 0, resources_by_type: {}, new_resources: 0 },
            posts: { total_posts: 0, popular_posts: [], new_posts: 0 }
          };
      }
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to fetch dashboard stats');
    }
  }
);

export const fetchSystemMetrics = createAsyncThunk(
  'dashboard/fetchSystemMetrics',
  async (_, { rejectWithValue }) => {
    try {
      // This would be replaced with actual API call
      return {
        cpu_usage: Math.random() * 100,
        memory_usage: Math.random() * 100,
        disk_usage: Math.random() * 100,
        active_users: Math.floor(Math.random() * 1000),
        response_time: Math.random() * 500
      };
    } catch (error) {
      return rejectWithValue('Failed to fetch system metrics');
    }
  }
);

export const fetchRecentActivity = createAsyncThunk(
  'dashboard/fetchRecentActivity',
  async (_, { rejectWithValue }) => {
    try {
      // This would be replaced with actual API call
      return [];
    } catch (error) {
      return rejectWithValue('Failed to fetch recent activity');
    }
  }
);

const dashboardSlice = createSlice({
  name: 'dashboard',
  initialState,
  reducers: {
    // Set dashboard role
    setRole: (state, action: PayloadAction<DashboardRole>) => {
      state.role = action.payload;
    },

    // Update configuration
    updateConfig: (state, action: PayloadAction<Partial<DashboardConfig>>) => {
      state.config = { ...state.config, ...action.payload };
    },

    // Set stats
    setStats: (state, action: PayloadAction<DashboardStat[]>) => {
      state.stats = action.payload;
    },

    // Set quick actions
    setQuickActions: (state, action: PayloadAction<DashboardQuickAction[]>) => {
      state.quickActions = action.payload;
    },

    // Set loading state
    setLoading: (state, action: PayloadAction<Partial<DashboardLoadingState>>) => {
      state.loading = { ...state.loading, ...action.payload };
    },

    // Add error
    addError: (state, action: PayloadAction<Omit<DashboardError, 'timestamp'>>) => {
      state.errors.push({
        ...action.payload,
        timestamp: Date.now()
      });
    },

    // Clear error
    clearError: (state, action: PayloadAction<string>) => {
      state.errors = state.errors.filter(error => error.section !== action.payload);
    },

    // Clear all errors
    clearAllErrors: (state) => {
      state.errors = [];
    },

    // Set system metrics
    setSystemMetrics: (state, action: PayloadAction<SystemMetrics>) => {
      state.systemMetrics = action.payload;
    },

    // Add security alert
    addSecurityAlert: (state, action: PayloadAction<SecurityAlert>) => {
      state.securityAlerts.unshift(action.payload);
    },

    // Resolve security alert
    resolveSecurityAlert: (state, action: PayloadAction<string>) => {
      const alert = state.securityAlerts.find(alert => alert.id === action.payload);
      if (alert) {
        alert.resolved = true;
      }
    },

    // Set recent activity
    setRecentActivity: (state, action: PayloadAction<RecentActivity[]>) => {
      state.recentActivity = action.payload;
    },

    // Add recent activity
    addRecentActivity: (state, action: PayloadAction<RecentActivity>) => {
      state.recentActivity.unshift(action.payload);
      // Keep only last 50 activities
      state.recentActivity = state.recentActivity.slice(0, 50);
    },

    // Set pending reports
    setPendingReports: (state, action: PayloadAction<PendingReport[]>) => {
      state.pendingReports = action.payload;
    },

    // Update report status
    updateReportStatus: (state, action: PayloadAction<{ id: string; status: PendingReport['status'] }>) => {
      const report = state.pendingReports.find(report => report.id === action.payload.id);
      if (report) {
        report.status = action.payload.status;
      }
    },

    // Set last refresh time
    setLastRefresh: (state) => {
      state.lastRefresh = Date.now();
    },

    // Clear general error
    clearGeneralError: (state) => {
      state.error = null;
    }
  },
  extraReducers: (builder) => {
    builder
      // Fetch dashboard stats
      .addCase(fetchDashboardStats.pending, (state) => {
        state.loading.stats = true;
        state.error = null;
      })
      .addCase(fetchDashboardStats.fulfilled, (state, action) => {
        state.loading.stats = false;
        // Transform API response to dashboard stats format
        const apiStats = action.payload;
        state.stats = [
          {
            id: 'total-users',
            title: 'Total Users',
            value: apiStats.users?.total_users || 0,
            icon: 'users'
          },
          {
            id: 'active-users',
            title: 'Active Users',
            value: apiStats.users?.active_users || 0,
            icon: 'user-check'
          },
          {
            id: 'total-events',
            title: 'Total Events',
            value: apiStats.events?.total_events || 0,
            icon: 'calendar'
          },
          {
            id: 'total-posts',
            title: 'Total Posts',
            value: apiStats.posts?.total_posts || 0,
            icon: 'message-square'
          }
        ];
      })
      .addCase(fetchDashboardStats.rejected, (state, action) => {
        state.loading.stats = false;
        state.error = action.payload as string;
      })
      // Fetch system metrics
      .addCase(fetchSystemMetrics.pending, (state) => {
        state.loading.sectionData.systemMetrics = true;
      })
      .addCase(fetchSystemMetrics.fulfilled, (state, action) => {
        state.loading.sectionData.systemMetrics = false;
        state.systemMetrics = action.payload;
      })
      .addCase(fetchSystemMetrics.rejected, (state, action) => {
        state.loading.sectionData.systemMetrics = false;
        state.errors.push({
          section: 'systemMetrics',
          message: action.payload as string,
          timestamp: Date.now()
        });
      })
      // Fetch recent activity
      .addCase(fetchRecentActivity.pending, (state) => {
        state.loading.sectionData.recentActivity = true;
      })
      .addCase(fetchRecentActivity.fulfilled, (state, action) => {
        state.loading.sectionData.recentActivity = false;
        state.recentActivity = action.payload;
      })
      .addCase(fetchRecentActivity.rejected, (state, action) => {
        state.loading.sectionData.recentActivity = false;
        state.errors.push({
          section: 'recentActivity',
          message: action.payload as string,
          timestamp: Date.now()
        });
      });
  }
});

// Export actions
export const {
  setRole,
  updateConfig,
  setStats,
  setQuickActions,
  setLoading,
  addError,
  clearError,
  clearAllErrors,
  setSystemMetrics,
  addSecurityAlert,
  resolveSecurityAlert,
  setRecentActivity,
  addRecentActivity,
  setPendingReports,
  updateReportStatus,
  setLastRefresh,
  clearGeneralError
} = dashboardSlice.actions;

// Selectors
export const selectDashboard = (state: { dashboard: DashboardState }) => state.dashboard;
export const selectDashboardRole = (state: { dashboard: DashboardState }) => state.dashboard.role;
export const selectDashboardConfig = (state: { dashboard: DashboardState }) => state.dashboard.config;
export const selectDashboardStats = (state: { dashboard: DashboardState }) => state.dashboard.stats;
export const selectDashboardQuickActions = (state: { dashboard: DashboardState }) => state.dashboard.quickActions;
export const selectDashboardLoading = (state: { dashboard: DashboardState }) => state.dashboard.loading;
export const selectDashboardErrors = (state: { dashboard: DashboardState }) => state.dashboard.errors;
export const selectSystemMetrics = (state: { dashboard: DashboardState }) => state.dashboard.systemMetrics;
export const selectSecurityAlerts = (state: { dashboard: DashboardState }) => state.dashboard.securityAlerts;
export const selectRecentActivity = (state: { dashboard: DashboardState }) => state.dashboard.recentActivity;
export const selectPendingReports = (state: { dashboard: DashboardState }) => state.dashboard.pendingReports;

// Helper selectors
export const selectIsLoading = (state: { dashboard: DashboardState }) =>
  state.dashboard.loading.global || state.dashboard.loading.stats || state.dashboard.loading.quickActions;

export const selectHasErrors = (state: { dashboard: DashboardState }) =>
  state.dashboard.errors.length > 0 || state.dashboard.error !== null;

export const selectUnresolvedSecurityAlerts = (state: { dashboard: DashboardState }) =>
  state.dashboard.securityAlerts.filter(alert => !alert.resolved);

export const selectPendingReportsCount = (state: { dashboard: DashboardState }) =>
  state.dashboard.pendingReports.filter(report => report.status === 'pending').length;

// Role-based selectors
export const selectIsAdmin = (state: { dashboard: DashboardState }) =>
  state.dashboard.role === 'admin' || state.dashboard.role === 'super_admin';

export const selectIsSuperAdmin = (state: { dashboard: DashboardState }) =>
  state.dashboard.role === 'super_admin';

export const selectIsModerator = (state: { dashboard: DashboardState }) =>
  state.dashboard.role === 'moderator';

export default dashboardSlice.reducer;
