import { configureStore } from '@reduxjs/toolkit';
import authReducer from './authSlice';
import eventsReducer from './eventsSlice';
import adminReducer from './adminSlice';
import languageReducer, { initializeLanguage } from './languageSlice';
import incubatorReducer from './incubatorSlice';
import forumReducer from './forumSlice';
import aiReducer from './aiSlice';
import businessPlansReducer from './businessPlansSlice';
import aiContextReducer from './aiContextSlice';
import dashboardReducer from './dashboardSlice';
import toastReducer from './toastSlice';
import uiReducer from './uiSlice';
// Simple Redux store
import { aiContextMiddleware } from '../middleware/aiContextMiddleware';

export const store = configureStore({
  reducer: {
    auth: authReducer,
    events: eventsReducer,
    admin: adminReducer,
    language: languageReducer,
    incubator: incubatorReducer,
    forum: forumReducer,
    ai: aiReducer,
    businessPlans: businessPlansReducer,
    aiContext: aiContextReducer,
    dashboard: dashboardReducer,
    toast: toastReducer,
    ui: uiReducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE'],
      },
    }).concat(aiContextMiddleware),
});

// Simple store - just Redux

// Initialize language after i18n is loaded
// We'll do this with a slight delay to ensure i18n is fully initialized
setTimeout(() => {
  initializeLanguage();
}, 100);

// Note: fetchLanguage() will be called after user authentication is established
// This prevents "Authentication credentials were not provided" errors

// Define RootState and AppDispatch types
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
