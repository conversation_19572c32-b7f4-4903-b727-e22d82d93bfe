import { createSlice, PayloadAction } from '@reduxjs/toolkit';

// Types for toast functionality
export type ToastType = 'success' | 'error' | 'info' | 'warning';

export interface ToastMessage {
  id: string;
  type: ToastType;
  message: string;
  duration?: number;
}

interface ToastState {
  toasts: ToastMessage[];
}

// Initial state
const initialState: ToastState = {
  toasts: []
};

// Helper function to generate unique ID
const generateId = () => Math.random().toString(36).substring(2, 9);

const toastSlice = createSlice({
  name: 'toast',
  initialState,
  reducers: {
    // Add a new toast
    addToast: (state, action: PayloadAction<Omit<ToastMessage, 'id'>>) => {
      const id = generateId();
      const toast: ToastMessage = {
        id,
        ...action.payload
      };
      state.toasts.push(toast);
    },

    // Remove a toast by ID
    removeToast: (state, action: PayloadAction<string>) => {
      state.toasts = state.toasts.filter(toast => toast.id !== action.payload);
    },

    // Clear all toasts
    clearToasts: (state) => {
      state.toasts = [];
    },

    // Add success toast (convenience action)
    addSuccessToast: (state, action: PayloadAction<{ message: string; duration?: number }>) => {
      const id = generateId();
      const toast: ToastMessage = {
        id,
        type: 'success',
        message: action.payload.message,
        duration: action.payload.duration
      };
      state.toasts.push(toast);
    },

    // Add error toast (convenience action)
    addErrorToast: (state, action: PayloadAction<{ message: string; duration?: number }>) => {
      const id = generateId();
      const toast: ToastMessage = {
        id,
        type: 'error',
        message: action.payload.message,
        duration: action.payload.duration
      };
      state.toasts.push(toast);
    },

    // Add info toast (convenience action)
    addInfoToast: (state, action: PayloadAction<{ message: string; duration?: number }>) => {
      const id = generateId();
      const toast: ToastMessage = {
        id,
        type: 'info',
        message: action.payload.message,
        duration: action.payload.duration
      };
      state.toasts.push(toast);
    },

    // Add warning toast (convenience action)
    addWarningToast: (state, action: PayloadAction<{ message: string; duration?: number }>) => {
      const id = generateId();
      const toast: ToastMessage = {
        id,
        type: 'warning',
        message: action.payload.message,
        duration: action.payload.duration
      };
      state.toasts.push(toast);
    }
  }
});

// Export actions
export const {
  addToast,
  removeToast,
  clearToasts,
  addSuccessToast,
  addErrorToast,
  addInfoToast,
  addWarningToast
} = toastSlice.actions;

// Selectors
export const selectToasts = (state: { toast: ToastState }) => state.toast.toasts;
export const selectToastCount = (state: { toast: ToastState }) => state.toast.toasts.length;
export const selectToastsByType = (type: ToastType) => (state: { toast: ToastState }) =>
  state.toast.toasts.filter(toast => toast.type === type);

export default toastSlice.reducer;

// Toast utility functions for external use (similar to the original toast object)
export const createToastActions = (dispatch: any) => ({
  success: (message: string, duration?: number) => {
    dispatch(addSuccessToast({ message, duration }));
  },
  error: (message: string, duration?: number) => {
    dispatch(addErrorToast({ message, duration }));
  },
  info: (message: string, duration?: number) => {
    dispatch(addInfoToast({ message, duration }));
  },
  warning: (message: string, duration?: number) => {
    dispatch(addWarningToast({ message, duration }));
  }
});

// Event-based toast system for compatibility with existing code
export interface ToastEvent {
  type: ToastType;
  message: string;
  duration?: number;
}

// Function to create toast events (for backward compatibility)
export const createToastEvent = (toast: ToastEvent): CustomEvent<ToastEvent> => {
  return new CustomEvent('toast', { detail: toast });
};

// Global toast functions that work outside React components (for backward compatibility)
export const toast = {
  success: (message: string, duration?: number) => {
    window.dispatchEvent(createToastEvent({ type: 'success', message, duration }));
  },
  error: (message: string, duration?: number) => {
    window.dispatchEvent(createToastEvent({ type: 'error', message, duration }));
  },
  info: (message: string, duration?: number) => {
    window.dispatchEvent(createToastEvent({ type: 'info', message, duration }));
  },
  warning: (message: string, duration?: number) => {
    window.dispatchEvent(createToastEvent({ type: 'warning', message, duration }));
  }
};
