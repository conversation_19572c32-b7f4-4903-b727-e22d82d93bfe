import { createSlice, PayloadAction } from '@reduxjs/toolkit';

// Types for UI state management
interface TabState {
  [tabGroupId: string]: string; // tabGroupId -> activeTabValue
}

interface ModalState {
  [modalId: string]: boolean; // modalId -> isOpen
}

interface ComponentState {
  [componentId: string]: any; // componentId -> component-specific state
}

interface UIState {
  tabs: TabState;
  modals: ModalState;
  components: ComponentState;
  sidebarOpen: boolean;
  sidebarCollapsed: boolean;
  theme: 'light' | 'dark' | 'auto';
  loading: {
    [key: string]: boolean;
  };
}

// Initial state
const initialState: UIState = {
  tabs: {},
  modals: {},
  components: {},
  sidebarOpen: true,
  sidebarCollapsed: false,
  theme: 'auto',
  loading: {}
};

const uiSlice = createSlice({
  name: 'ui',
  initialState,
  reducers: {
    // Tab management
    setActiveTab: (state, action: PayloadAction<{ tabGroupId: string; value: string }>) => {
      state.tabs[action.payload.tabGroupId] = action.payload.value;
    },

    // Remove tab group
    removeTabGroup: (state, action: PayloadAction<string>) => {
      delete state.tabs[action.payload];
    },

    // Modal management
    openModal: (state, action: PayloadAction<string>) => {
      state.modals[action.payload] = true;
    },

    closeModal: (state, action: PayloadAction<string>) => {
      state.modals[action.payload] = false;
    },

    toggleModal: (state, action: PayloadAction<string>) => {
      state.modals[action.payload] = !state.modals[action.payload];
    },

    // Close all modals
    closeAllModals: (state) => {
      Object.keys(state.modals).forEach(modalId => {
        state.modals[modalId] = false;
      });
    },

    // Component state management
    setComponentState: (state, action: PayloadAction<{ componentId: string; state: any }>) => {
      state.components[action.payload.componentId] = action.payload.state;
    },

    updateComponentState: (state, action: PayloadAction<{ componentId: string; updates: any }>) => {
      const currentState = state.components[action.payload.componentId] || {};
      state.components[action.payload.componentId] = {
        ...currentState,
        ...action.payload.updates
      };
    },

    removeComponentState: (state, action: PayloadAction<string>) => {
      delete state.components[action.payload];
    },

    // Sidebar management
    setSidebarOpen: (state, action: PayloadAction<boolean>) => {
      state.sidebarOpen = action.payload;
    },

    toggleSidebar: (state) => {
      state.sidebarOpen = !state.sidebarOpen;
    },

    setSidebarCollapsed: (state, action: PayloadAction<boolean>) => {
      state.sidebarCollapsed = action.payload;
    },

    toggleSidebarCollapsed: (state) => {
      state.sidebarCollapsed = !state.sidebarCollapsed;
    },

    // Theme management
    setTheme: (state, action: PayloadAction<'light' | 'dark' | 'auto'>) => {
      state.theme = action.payload;
    },

    // Loading state management
    setLoading: (state, action: PayloadAction<{ key: string; loading: boolean }>) => {
      state.loading[action.payload.key] = action.payload.loading;
    },

    clearLoading: (state, action: PayloadAction<string>) => {
      delete state.loading[action.payload];
    },

    clearAllLoading: (state) => {
      state.loading = {};
    }
  }
});

// Export actions
export const {
  setActiveTab,
  removeTabGroup,
  openModal,
  closeModal,
  toggleModal,
  closeAllModals,
  setComponentState,
  updateComponentState,
  removeComponentState,
  setSidebarOpen,
  toggleSidebar,
  setSidebarCollapsed,
  toggleSidebarCollapsed,
  setTheme,
  setLoading,
  clearLoading,
  clearAllLoading
} = uiSlice.actions;

// Selectors
export const selectUI = (state: { ui: UIState }) => state.ui;

// Tab selectors
export const selectActiveTab = (tabGroupId: string) => (state: { ui: UIState }) =>
  state.ui.tabs[tabGroupId];

export const selectAllTabs = (state: { ui: UIState }) => state.ui.tabs;

// Modal selectors
export const selectModalState = (modalId: string) => (state: { ui: UIState }) =>
  state.ui.modals[modalId] || false;

export const selectAllModals = (state: { ui: UIState }) => state.ui.modals;

export const selectAnyModalOpen = (state: { ui: UIState }) =>
  Object.values(state.ui.modals).some(isOpen => isOpen);

// Component state selectors
export const selectComponentState = (componentId: string) => (state: { ui: UIState }) =>
  state.ui.components[componentId];

export const selectAllComponentStates = (state: { ui: UIState }) => state.ui.components;

// Sidebar selectors
export const selectSidebarOpen = (state: { ui: UIState }) => state.ui.sidebarOpen;
export const selectSidebarCollapsed = (state: { ui: UIState }) => state.ui.sidebarCollapsed;

// Theme selector
export const selectTheme = (state: { ui: UIState }) => state.ui.theme;

// Loading selectors
export const selectLoading = (key: string) => (state: { ui: UIState }) =>
  state.ui.loading[key] || false;

export const selectAllLoading = (state: { ui: UIState }) => state.ui.loading;

export const selectAnyLoading = (state: { ui: UIState }) =>
  Object.values(state.ui.loading).some(loading => loading);

export default uiSlice.reducer;

// Helper functions for tabs (to replace TabsContext functionality)
export const createTabsActions = (dispatch: any, tabGroupId: string) => ({
  setActiveTab: (value: string) => dispatch(setActiveTab({ tabGroupId, value })),
  removeTabGroup: () => dispatch(removeTabGroup(tabGroupId))
});

// Helper functions for modals
export const createModalActions = (dispatch: any, modalId: string) => ({
  open: () => dispatch(openModal(modalId)),
  close: () => dispatch(closeModal(modalId)),
  toggle: () => dispatch(toggleModal(modalId))
});

// Helper functions for component state
export const createComponentActions = (dispatch: any, componentId: string) => ({
  setState: (state: any) => dispatch(setComponentState({ componentId, state })),
  updateState: (updates: any) => dispatch(updateComponentState({ componentId, updates })),
  removeState: () => dispatch(removeComponentState(componentId))
});
