/**
 * Translation Auditor
 * Comprehensive tool to identify missing translation keys and inconsistencies
 */

interface TranslationFile {
  [key: string]: any;
}

interface AuditResult {
  missingKeys: {
    en: string[];
    ar: string[];
  };
  extraKeys: {
    en: string[];
    ar: string[];
  };
  totalKeys: {
    en: number;
    ar: number;
    unique: number;
  };
  coverage: {
    en: number;
    ar: number;
  };
  files: {
    [filename: string]: {
      en: string[];
      ar: string[];
      missing: {
        en: string[];
        ar: string[];
      };
    };
  };
}

/**
 * Extract all keys from a nested object
 */
function extractKeys(obj: any, prefix = ''): string[] {
  const keys: string[] = [];
  
  if (typeof obj !== 'object' || obj === null) {
    return keys;
  }
  
  for (const [key, value] of Object.entries(obj)) {
    const fullKey = prefix ? `${prefix}.${key}` : key;
    
    if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
      keys.push(...extractKeys(value, fullKey));
    } else {
      keys.push(fullKey);
    }
  }
  
  return keys;
}

/**
 * Load translation file dynamically
 */
async function loadTranslationFile(lang: string, filename: string): Promise<TranslationFile> {
  try {
    const module = await import(`../locales/${lang}/${filename}`);
    return module.default || module;
  } catch (error) {
    console.warn(`Failed to load ${lang}/${filename}:`, error);
    return {};
  }
}

/**
 * Get all translation files
 */
function getTranslationFiles(): string[] {
  return [
    'common.json',
    'auth.json',
    'navigation.json',
    'dashboard.json',
    'ai.json',
    'admin.json',
    'mentorship.json',
    'templates.json',
    'home.json',
    'hero.json',
    'features.json',
    'analytics.json',
    'incubator.json',
    'posts.json',
    'events.json',
    'resources.json',
    'profile.json',
    'about.json',
    'businessPlan.json',
    'crud.json',
    'funding.json',
    'roles.json',
    'businessPlans.json',
    'investments.json',
    'mentees.json',
    'moderation.json',
    'portfolio.json',
    'reports.json',
    'superAdmin.json',
    'community.json',
    'footer.json',
    'contact.json',
    'forum.json'
  ];
}

/**
 * Audit all translation files
 */
export async function auditTranslations(): Promise<AuditResult> {
  const files = getTranslationFiles();
  const result: AuditResult = {
    missingKeys: { en: [], ar: [] },
    extraKeys: { en: [], ar: [] },
    totalKeys: { en: 0, ar: 0, unique: 0 },
    coverage: { en: 0, ar: 0 },
    files: {}
  };

  const allKeys = new Set<string>();
  const enKeys = new Set<string>();
  const arKeys = new Set<string>();

  // Process each file
  for (const filename of files) {
    console.log(`🔍 Auditing ${filename}...`);
    
    const [enTranslations, arTranslations] = await Promise.all([
      loadTranslationFile('en', filename),
      loadTranslationFile('ar', filename)
    ]);

    const enFileKeys = extractKeys(enTranslations);
    const arFileKeys = extractKeys(arTranslations);

    // Add to global sets
    enFileKeys.forEach(key => {
      enKeys.add(key);
      allKeys.add(key);
    });
    
    arFileKeys.forEach(key => {
      arKeys.add(key);
      allKeys.add(key);
    });

    // Find missing keys for this file
    const fileAllKeys = new Set([...enFileKeys, ...arFileKeys]);
    const enMissing = Array.from(fileAllKeys).filter(key => !enFileKeys.includes(key));
    const arMissing = Array.from(fileAllKeys).filter(key => !arFileKeys.includes(key));

    result.files[filename] = {
      en: enFileKeys,
      ar: arFileKeys,
      missing: {
        en: enMissing,
        ar: arMissing
      }
    };

    // Add to global missing keys
    result.missingKeys.en.push(...enMissing);
    result.missingKeys.ar.push(...arMissing);
  }

  // Calculate totals and coverage
  result.totalKeys.en = enKeys.size;
  result.totalKeys.ar = arKeys.size;
  result.totalKeys.unique = allKeys.size;

  result.coverage.en = (enKeys.size / allKeys.size) * 100;
  result.coverage.ar = (arKeys.size / allKeys.size) * 100;

  // Find extra keys (keys that exist in one language but not the other)
  result.extraKeys.en = Array.from(enKeys).filter(key => !arKeys.has(key));
  result.extraKeys.ar = Array.from(arKeys).filter(key => !enKeys.has(key));

  return result;
}

/**
 * Print audit report to console
 */
export function printAuditReport(result: AuditResult): void {
  console.log('\n🔍 Translation Audit Report');
  console.log('=' .repeat(50));
  
  console.log('\n📊 Summary:');
  console.log(`Total unique keys: ${result.totalKeys.unique}`);
  console.log(`English keys: ${result.totalKeys.en} (${result.coverage.en.toFixed(1)}% coverage)`);
  console.log(`Arabic keys: ${result.totalKeys.ar} (${result.coverage.ar.toFixed(1)}% coverage)`);
  
  if (result.missingKeys.en.length > 0) {
    console.log(`\n❌ Missing in English: ${result.missingKeys.en.length} keys`);
    result.missingKeys.en.slice(0, 10).forEach(key => console.log(`  - ${key}`));
    if (result.missingKeys.en.length > 10) {
      console.log(`  ... and ${result.missingKeys.en.length - 10} more`);
    }
  }
  
  if (result.missingKeys.ar.length > 0) {
    console.log(`\n❌ Missing in Arabic: ${result.missingKeys.ar.length} keys`);
    result.missingKeys.ar.slice(0, 10).forEach(key => console.log(`  - ${key}`));
    if (result.missingKeys.ar.length > 10) {
      console.log(`  ... and ${result.missingKeys.ar.length - 10} more`);
    }
  }

  // File-by-file breakdown
  console.log('\n📁 File-by-file breakdown:');
  Object.entries(result.files).forEach(([filename, fileData]) => {
    const enMissing = fileData.missing.en.length;
    const arMissing = fileData.missing.ar.length;
    
    if (enMissing > 0 || arMissing > 0) {
      console.log(`\n  ${filename}:`);
      if (enMissing > 0) console.log(`    EN missing: ${enMissing} keys`);
      if (arMissing > 0) console.log(`    AR missing: ${arMissing} keys`);
    }
  });

  console.log('\n✅ Recommendations:');
  if (result.missingKeys.en.length > 0) {
    console.log('1. Add missing English translation keys');
  }
  if (result.missingKeys.ar.length > 0) {
    console.log('2. Add missing Arabic translation keys');
  }
  if (result.coverage.en < 100 || result.coverage.ar < 100) {
    console.log('3. Ensure 100% translation coverage for both languages');
  }
  console.log('4. Run this audit regularly to catch missing translations early');
}

/**
 * Generate missing keys for a specific language
 */
export function generateMissingKeysTemplate(result: AuditResult, lang: 'en' | 'ar'): string {
  const missingKeys = result.missingKeys[lang];
  const template: any = {};
  
  missingKeys.forEach(key => {
    const parts = key.split('.');
    let current = template;
    
    for (let i = 0; i < parts.length - 1; i++) {
      if (!current[parts[i]]) {
        current[parts[i]] = {};
      }
      current = current[parts[i]];
    }
    
    current[parts[parts.length - 1]] = `[MISSING: ${key}]`;
  });
  
  return JSON.stringify(template, null, 2);
}

// Export for console debugging
if (typeof window !== 'undefined') {
  (window as any).translationAuditor = {
    audit: auditTranslations,
    print: printAuditReport,
    generateTemplate: generateMissingKeysTemplate
  };
  console.log('🔍 Translation auditor loaded. Use translationAuditor in console.');
}

export default { auditTranslations, printAuditReport, generateMissingKeysTemplate };
